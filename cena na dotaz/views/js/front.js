/**
 * JavaScript pro frontend modul "Cena na dotaz"
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Inicializace modulu
    PriceInquiry.init();
    
});

/**
 * Hlavní objekt pro správu dotazů na cenu
 */
var PriceInquiry = {
    
    /**
     * Inicializace modulu
     */
    init: function() {
        this.bindEvents();
        this.hideAddToCartButtons();
    },
    
    /**
     * Navázání event listenerů
     */
    bindEvents: function() {
        // Kliknutí na tlačítko "Zjistit cenu"
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('price-inquiry-btn') || 
                e.target.closest('.price-inquiry-btn')) {
                
                e.preventDefault();
                e.stopPropagation();
                
                var button = e.target.classList.contains('price-inquiry-btn') ? 
                           e.target : e.target.closest('.price-inquiry-btn');
                
                PriceInquiry.handleInquiryClick(button);
            }
        });
    },
    
    /**
     * Zpracování kliknutí na tlačítko "Zjistit cenu"
     */
    handleInquiryClick: function(button) {
        var productId = button.getAttribute('data-product-id');
        var productName = button.getAttribute('data-product-name');
        var isLogged = button.getAttribute('data-is-logged') === '1';
        var customerName = button.getAttribute('data-customer-name') || '';
        var customerEmail = button.getAttribute('data-customer-email') || '';
        
        // Zobrazení loading stavu
        this.setButtonLoading(button, true);
        
        // Simulace načítání (v kroku 3 bude nahrazeno skutečným popup formulářem)
        setTimeout(function() {
            PriceInquiry.setButtonLoading(button, false);
            
            // Dočasné řešení - alert (v kroku 3 bude nahrazeno popup formulářem)
            alert('Popup formulář pro dotaz na cenu bude implementován v kroku 3.\n\n' +
                  'Produkt: ' + productName + '\n' +
                  'ID: ' + productId + '\n' +
                  'Přihlášen: ' + (isLogged ? 'Ano' : 'Ne') +
                  (isLogged ? '\nJméno: ' + customerName + '\nE-mail: ' + customerEmail : ''));
        }, 500);
    },
    
    /**
     * Nastavení loading stavu tlačítka
     */
    setButtonLoading: function(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            
            var originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="material-icons">hourglass_empty</i> Načítání...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            
            var originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    },
    
    /**
     * Skrytí tlačítek "Přidat do košíku" pro produkty s cenou 0
     */
    hideAddToCartButtons: function() {
        // Najdeme všechny produkty s cenou na dotaz
        var priceInquiryContainers = document.querySelectorAll('.price-inquiry-container');
        
        priceInquiryContainers.forEach(function(container) {
            // Najdeme nejbližší product container
            var productContainer = container.closest('.product-miniature, .product-container, .product, .js-product');
            
            if (productContainer) {
                // Skryjeme všechna tlačítka pro přidání do košíku v tomto produktu
                var addToCartButtons = productContainer.querySelectorAll(
                    '.add-to-cart, .product-add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
                );
                
                addToCartButtons.forEach(function(btn) {
                    btn.style.display = 'none';
                });
            }
        });
        
        // Také skryjeme tlačítka na stránce produktu
        if (document.querySelector('.price-inquiry-btn')) {
            var productPageButtons = document.querySelectorAll(
                '.product-add-to-cart, .add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
            );
            
            productPageButtons.forEach(function(btn) {
                btn.style.display = 'none';
            });
        }
    }
    
};

/**
 * CSS styly pro loading stav (přidáno dynamicky)
 */
if (!document.querySelector('#price-inquiry-dynamic-styles')) {
    var style = document.createElement('style');
    style.id = 'price-inquiry-dynamic-styles';
    style.textContent = `
        .price-inquiry-btn.loading {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .price-inquiry-btn.loading:hover {
            transform: none;
            box-shadow: none;
        }
    `;
    document.head.appendChild(style);
}
