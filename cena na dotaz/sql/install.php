<?php
/**
 * SQL skript pro vytvoření tabulky dotazů na cenu
 */

$sql = array();

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'price_inquiry` (
    `id_inquiry` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT 0,
    `id_customer` int(10) unsigned DEFAULT 0,
    `customer_name` varchar(255) NOT NULL,
    `customer_email` varchar(255) NOT NULL,
    `customer_phone` varchar(50) DEFAULT NULL,
    `message` text DEFAULT NULL,
    `date_add` datetime NOT NULL,
    `status` enum("new","processed") NOT NULL DEFAULT "new",
    PRIMARY KEY (`id_inquiry`),
    KEY `id_product` (`id_product`),
    KEY `id_customer` (`id_customer`),
    KEY `status` (`status`),
    KEY `date_add` (`date_add`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}