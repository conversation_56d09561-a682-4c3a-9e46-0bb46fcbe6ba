<?php
/**
 * <PERSON><PERSON><PERSON> "Cena na dotaz" pro PrestaShop 8.2.0
 * Zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje dotaz na cenu
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class PriceInquiry extends Module
{
    public function __construct()
    {
        $this->name = 'priceinquiry';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = 'Custom Module';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '8.2.0',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Cena na dotaz');
        $this->description = $this->l('Zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje zákazníkům poslat dotaz na cenu.');
        $this->confirmUninstall = $this->l('Opravdu chcete odinstalovat modul? Všechny dotazy budou smazány.');
    }

    public function install()
    {
        include(dirname(__FILE__).'/sql/install.php');
        
        return parent::install() &&
            $this->registerHook('displayProductAdditionalInfo') &&
            $this->registerHook('displayProductPriceBlock') &&
            $this->registerHook('displayHeader') &&
            Configuration::updateValue('PRICE_INQUIRY_ADMIN_EMAIL', Configuration::get('PS_SHOP_EMAIL')) &&
            Configuration::updateValue('PRICE_INQUIRY_ENABLED', 1);
    }

    public function uninstall()
    {
        $sql = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'price_inquiry`';
        Db::getInstance()->execute($sql);
        
        return Configuration::deleteByName('PRICE_INQUIRY_ADMIN_EMAIL') &&
            Configuration::deleteByName('PRICE_INQUIRY_ENABLED') &&
            parent::uninstall();
    }

    public function hookDisplayHeader()
    {
        // Přidání CSS a JS souborů
        $this->context->controller->addCSS($this->_path.'views/css/front.css');
        $this->context->controller->addJS($this->_path.'views/js/front.js');
    }

    public function hookDisplayProductPriceBlock($params)
    {
        // Detekce produktu s cenou 0 a skrytí původní ceny
        $product = $params['product'];
        
        if (isset($product['price_amount']) && $product['price_amount'] == 0) {
            return '<span class="price-inquiry-text">'.$this->l('Cena na dotaz').'</span>';
        }
        
        return '';
    }

    public function hookDisplayProductAdditionalInfo($params)
    {
        // Zobrazení tlačítka "Zjistit cenu" pro produkty s cenou 0
        $product = $this->context->controller->getProduct();
        
        if ($product && $product->price == 0) {
            $this->context->smarty->assign([
                'product_id' => $product->id,
                'product_name' => $product->name,
                'is_logged' => $this->context->customer->isLogged(),
                'customer_name' => $this->context->customer->firstname.' '.$this->context->customer->lastname,
                'customer_email' => $this->context->customer->email
            ]);
            
            return $this->display(__FILE__, 'views/templates/front/price_inquiry_button.tpl');
        }
        
        return '';
    }

    public function getContent()
    {
        // Konfigurace modulu v admin rozhraní (bude implementováno v kroku 4)
        $output = '';
        
        if (Tools::isSubmit('submit'.$this->name)) {
            $admin_email = Tools::getValue('PRICE_INQUIRY_ADMIN_EMAIL');
            $enabled = Tools::getValue('PRICE_INQUIRY_ENABLED');
            
            Configuration::updateValue('PRICE_INQUIRY_ADMIN_EMAIL', $admin_email);
            Configuration::updateValue('PRICE_INQUIRY_ENABLED', $enabled);
            
            $output .= $this->displayConfirmation($this->l('Nastavení bylo uloženo.'));
        }
        
        return $output.$this->displayForm();
    }

    public function displayForm()
    {
        // Základní formulář pro konfiguraci (rozšířeno v kroku 4)
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => $this->l('Nastavení'),
                ],
                'input' => [
                    [
                        'type' => 'text',
                        'label' => $this->l('E-mail administrátora'),
                        'name' => 'PRICE_INQUIRY_ADMIN_EMAIL',
                        'required' => true
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Povolit modul'),
                        'name' => 'PRICE_INQUIRY_ENABLED',
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            ]
                        ]
                    ]
                ],
                'submit' => [
                    'title' => $this->l('Uložit'),
                ]
            ]
        ];

        $helper = new HelperForm();
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex.'&configure='.$this->name;
        $helper->submit_action = 'submit'.$this->name;
        $helper->default_form_language = $this->context->language->id;

        $helper->fields_value['PRICE_INQUIRY_ADMIN_EMAIL'] = Configuration::get('PRICE_INQUIRY_ADMIN_EMAIL');
        $helper->fields_value['PRICE_INQUIRY_ENABLED'] = Configuration::get('PRICE_INQUIRY_ENABLED');

        return $helper->generateForm([$fields_form]);
    }
}